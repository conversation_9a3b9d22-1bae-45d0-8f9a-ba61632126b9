#include <iostream>
#include <torch/torch.h>
#include "Factor.h"
#include "BasicFactors.h"
#include "Engine.h"
#include "Rolling.h"
#include "Utils.h"

int main() {
    std::cout << "Hello from spectre_cpp!" << std::endl;

    // Simple LibTorch test
    torch::Tensor tensor = torch::rand({2, 3});
    std::cout << "Random tensor:\n" << tensor << std::endl;

    // Test basic utility functions
    std::cout << "\n--- Testing Utility Functions ---\n";
    torch::Tensor test_data = torch::tensor({{1.0, 2.0, std::numeric_limits<float>::quiet_NaN(), 4.0},
                                            {5.0, std::numeric_limits<float>::quiet_NaN(), 7.0, 8.0}});
    std::cout << "Test data with NaN:\n" << test_data << std::endl;
    std::cout << "nanmean (dim=1):\n" << Spectre::nanmean(test_data, 1) << std::endl;
    std::cout << "nansum (dim=1):\n" << Spectre::nansum(test_data, 1) << std::endl;
    std::cout << "nanstd (dim=1):\n" << Spectre::nanstd(test_data, 1) << std::endl;

    // Test Rolling class
    std::cout << "\n--- Testing Rolling Class ---\n";
    torch::Tensor rolling_data = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0},
                                               {10.0, 20.0, 30.0, 40.0, 50.0}});
    std::cout << "Rolling data:\n" << rolling_data << std::endl;

    Spectre::Rolling rolling(rolling_data, 3);
    std::cout << "Rolling nanmean (window=3):\n" << rolling.nanmean() << std::endl;
    std::cout << "Rolling nansum (window=3):\n" << rolling.nansum() << std::endl;
    std::cout << "Rolling first:\n" << rolling.first() << std::endl;
    std::cout << "Rolling last:\n" << rolling.last() << std::endl;

    // Test AbsFactor
    std::cout << "\n--- Testing AbsFactor ---\n";
    torch::Tensor test_data_abs = torch::tensor({{-1.0, 2.0, -3.0}, {4.0, -5.0, 6.0}});
    Spectre::AbsFactor abs_factor(nullptr);
    torch::Tensor result_abs = abs_factor.compute({test_data_abs});
    std::cout << "Original: \n" << test_data_abs << std::endl;
    std::cout << "Abs: \n" << result_abs << std::endl;

    // Test LogFactor
    std::cout << "\n--- Testing LogFactor ---\n";
    torch::Tensor test_data_log = torch::tensor({{1.0, 2.0, 3.0}, {4.0, 5.0, 6.0}});
    Spectre::LogFactor log_factor(nullptr);
    torch::Tensor result_log = log_factor.compute({test_data_log});
    std::cout << "Original: \n" << test_data_log << std::endl;
    std::cout << "Log: \n" << result_log << std::endl;

    // Test ShiftFactor
    std::cout << "\n--- Testing ShiftFactor ---\n";
    torch::Tensor test_data_shift = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0}, {6.0, 7.0, 8.0, 9.0, 10.0}});
    Spectre::ShiftFactor shift_factor_pos(nullptr, 2);
    torch::Tensor result_shift_pos = shift_factor_pos.compute({test_data_shift});
    std::cout << "Original: \n" << test_data_shift << std::endl;
    std::cout << "Shift (periods=2): \n" << result_shift_pos << std::endl;

    Spectre::ShiftFactor shift_factor_neg(nullptr, -1);
    torch::Tensor result_shift_neg = shift_factor_neg.compute({test_data_shift});
    std::cout << "Shift (periods=-1): \n" << result_shift_neg << std::endl;

    // Test SimpleMovingAverage
    std::cout << "\n--- Testing SimpleMovingAverage ---\n";
    torch::Tensor test_data_sma = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0},
                                                {10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0}});
    // Add NaN values for testing
    test_data_sma[0][2] = std::numeric_limits<float>::quiet_NaN();
    test_data_sma[1][4] = std::numeric_limits<float>::quiet_NaN();

    Spectre::SimpleMovingAverage sma_factor(nullptr, 3);
    torch::Tensor result_sma = sma_factor.compute({test_data_sma});
    std::cout << "Original: \n" << test_data_sma << std::endl;
    std::cout << "SMA (win=3): \n" << result_sma << std::endl;

    // Test Returns factor
    std::cout << "\n--- Testing Returns Factor ---\n";
    torch::Tensor price_data = torch::tensor({{100.0, 102.0, 101.0, 103.0, 105.0},
                                             {50.0, 51.0, 49.0, 52.0, 53.0}});
    Spectre::Returns returns_factor(nullptr);
    torch::Tensor result_returns = returns_factor.compute({price_data});
    std::cout << "Price data: \n" << price_data << std::endl;
    std::cout << "Returns: \n" << result_returns << std::endl;

    std::cout << "\nAll tests completed successfully!" << std::endl;
    return 0;
}
