#pragma once

#include <torch/torch.h>
#include <string>
#include <memory>
#include <unordered_map>

// Forward declarations
class BaseFactor;
class CustomFactor;

namespace Spectre {



// ============================================================================
// OHLCV���ݽṹ - ��ӦPython�е�engine.OHLCV
// ============================================================================

class OHLCV {
public:
    // ��̬��Ա����ʾ��ͬ�ļ۸�����
    static std::shared_ptr<BaseFactor> open;
    static std::shared_ptr<BaseFactor> high;
    static std::shared_ptr<BaseFactor> low;
    static std::shared_ptr<BaseFactor> close;
    static std::shared_ptr<BaseFactor> volume;
    
    // ��ʼ��OHLCV����
    static void initialize();
};

// ============================================================================
// �������� - ��ӦPython�е�DataFactor
// ============================================================================

class DataFactor : public BaseFactor {
public:
    DataFactor(const std::string& column_name);
    
    // ʵ��BaseFactor�Ĵ��麯��
    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override;
    int get_total_backwards() const override { return 0; } // �������Ӳ���Ҫ��ʷ����
    
    const std::string& get_column_name() const { return m_column_name; }

private:
    std::string m_column_name;
};

// ============================================================================
// �������� - ��ӦPython�е�FactorEngine
// ============================================================================

class FactorEngine {
public:
    FactorEngine();
    ~FactorEngine();
    
    // ��������
    void set_data(const torch::Tensor& data, const std::vector<std::string>& column_names);
    
    // �������Ӽ���
    torch::Tensor run(std::shared_ptr<BaseFactor> factor, 
                      const std::string& start_date = "", 
                      const std::string& end_date = "");
    
    // ��ȡ����
    torch::Tensor get_data(const std::string& column_name) const;
    
    // �豸����
    void set_device(const torch::Device& device) { m_device = device; }
    torch::Device get_device() const { return m_device; }
    
    // �������
    void column_to_parallel_groupby(const std::string& groupby);
    torch::Tensor group_by(const torch::Tensor& data, const std::string& groupby);
    torch::Tensor revert(const torch::Tensor& data, const std::string& groupby, const std::string& factor_name);
    torch::Tensor revert_to_series(const torch::Tensor& data, const std::string& groupby, const std::string& factor_name);
    
    // �������
    torch::Tensor get_group_padding_mask(const std::string& groupby);
    std::shared_ptr<BaseFactor> get_filter();
    
    // �������
    std::string cache_hash;

private:
    torch::Tensor m_data;
    std::vector<std::string> m_column_names;
    std::unordered_map<std::string, int> m_column_index_map;
    torch::Device m_device;
    std::shared_ptr<BaseFactor> m_universe_filter;
    
    // �ڲ���������
    void build_column_index_map();
};

// Forward declaration of Rolling class (defined in Rolling.h)
class Rolling;

} // namespace Spectre
