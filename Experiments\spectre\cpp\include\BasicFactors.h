#pragma once

#include "Factor.h"
#include "Utils.h"
#include <torch/torch.h>
#include <limits>

namespace Spectre {

// AbsFactor: 对应Python中的AbsFactor
class AbsFactor : public CustomFactor {
public:
    AbsFactor(const std::shared_ptr<BaseFactor>& input)
        : CustomFactor(1, {input}) {}

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        // 确保只有一个输入
        if (inputs.size() != 1) {
            throw std::runtime_error("AbsFactor expects exactly one input.");
        }
        return inputs[0].abs();
    }
};

// LogFactor: 对应Python中的LogFactor
class LogFactor : public CustomFactor {
public:
    LogFactor(const std::shared_ptr<BaseFactor>& input)
        : CustomFactor(1, {input}) {}

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        // 确保只有一个输入
        if (inputs.size() != 1) {
            throw std::runtime_error("LogFactor expects exactly one input.");
        }
        return inputs[0].log();
    }
};

// ShiftFactor: 对应Python中的ShiftFactor
class ShiftFactor : public CustomFactor {
public:
    ShiftFactor(const std::shared_ptr<BaseFactor>& input, int periods = 1)
        : CustomFactor(1, {input}), m_periods(periods) {}

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("ShiftFactor expects exactly one input.");
        }
        torch::Tensor data = inputs[0];

        // Python版本中对int类型有限制，这里也加上
        if (data.dtype() == torch::kInt || data.dtype() == torch::kLong) {
            throw std::runtime_error("ShiftFactor does not support `int` type, please convert to float.");
        }

        torch::Tensor shifted = data.roll(m_periods, /*dims=*/1);

        // 根据periods的方向填充NaN
        if (m_periods > 0) {
            shifted.slice(/*dim=*/1, /*start=*/0, /*end=*/m_periods).fill_(std::numeric_limits<float>::quiet_NaN());
        } else if (m_periods < 0) {
            shifted.slice(/*dim=*/1, /*start=*/shifted.size(1) + m_periods).fill_(std::numeric_limits<float>::quiet_NaN());
        }
        return shifted;
    }

private:
    int m_periods;
};

// SimpleMovingAverage: Corresponds to Python's SimpleMovingAverage
class SimpleMovingAverage : public CustomFactor {
public:
    SimpleMovingAverage(const std::shared_ptr<BaseFactor>& input, int win)
        : CustomFactor(win, {input}) {
        m_min_win = 2;
        if (win < m_min_win) {
            throw std::runtime_error("SimpleMovingAverage requires win >= 2.");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("SimpleMovingAverage expects exactly one input.");
        }

        // If input is already a Rolling object (from format_input), use it directly
        if (inputs[0].dim() == 3) {
            // This is unfolded data [assets, time_steps, window]
            return Spectre::nanmean(inputs[0], 2, false);
        } else {
            // Create rolling window manually
            torch::Tensor data = inputs[0];
            auto unfolded = data.unfold(1, m_win, 1);
            torch::Tensor result = Spectre::nanmean(unfolded, 2, false);

            // Pad with NaN for the first win-1 positions
            torch::Tensor padded_result = torch::full({data.size(0), data.size(1)},
                                                     std::numeric_limits<float>::quiet_NaN(),
                                                     torch::TensorOptions().dtype(result.dtype()).device(result.device()));
            padded_result.slice(1, m_win - 1).copy_(result);
            return padded_result;
        }
    }
};

// ============================================================================
// More Basic Factors - Corresponding to Python's basic.py
// ============================================================================

// Returns: Calculate returns by tick (not by time)
class Returns : public CustomFactor {
public:
    Returns(const std::shared_ptr<BaseFactor>& close_factor)
        : CustomFactor(2, {close_factor}) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("Returns expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            // Rolling data: [assets, time_steps, window=2]
            torch::Tensor data = inputs[0];
            torch::Tensor last_val = data.select(2, -1);  // Last value
            torch::Tensor first_val = data.select(2, 0);  // First value
            return last_val / first_val - 1.0;
        } else {
            // Regular data: calculate manually
            torch::Tensor data = inputs[0];
            torch::Tensor shifted = data.roll(1, 1);
            shifted.slice(1, 0, 1).fill_(std::numeric_limits<float>::quiet_NaN());
            return data / shifted - 1.0;
        }
    }
};

// LogReturns: Calculate log returns
class LogReturns : public CustomFactor {
public:
    LogReturns(const std::shared_ptr<BaseFactor>& close_factor)
        : CustomFactor(2, {close_factor}) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("LogReturns expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            // Rolling data
            torch::Tensor data = inputs[0];
            torch::Tensor last_val = data.select(2, -1);
            torch::Tensor first_val = data.select(2, 0);
            return (last_val / first_val).log();
        } else {
            // Regular data
            torch::Tensor data = inputs[0];
            torch::Tensor shifted = data.roll(1, 1);
            shifted.slice(1, 0, 1).fill_(std::numeric_limits<float>::quiet_NaN());
            return (data / shifted).log();
        }
    }
};

// VWAP: Volume Weighted Average Price
class VWAP : public CustomFactor {
public:
    VWAP(const std::shared_ptr<BaseFactor>& price_factor,
         const std::shared_ptr<BaseFactor>& volume_factor, int win = 1)
        : CustomFactor(win, {price_factor, volume_factor}) {
        if (win > 1) {
            m_min_win = 2;
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("VWAP expects exactly two inputs (price and volume).");
        }

        torch::Tensor price = inputs[0];
        torch::Tensor volume = inputs[1];

        if (m_win == 1) {
            return price * volume;
        } else {
            // For rolling VWAP
            if (price.dim() == 3 && volume.dim() == 3) {
                torch::Tensor price_volume = price * volume;
                torch::Tensor total_pv = Spectre::nansum(price_volume, 2, false);
                torch::Tensor total_vol = Spectre::nansum(volume, 2, false);
                return total_pv / total_vol;
            } else {
                throw std::runtime_error("Rolling VWAP requires 3D input tensors");
            }
        }
    }
};

// AverageDollarVolume: Average dollar volume
class AverageDollarVolume : public CustomFactor {
public:
    AverageDollarVolume(const std::shared_ptr<BaseFactor>& close_factor,
                       const std::shared_ptr<BaseFactor>& volume_factor, int win = 1)
        : CustomFactor(win, {close_factor, volume_factor}) {
        if (win > 1) {
            m_min_win = 2;
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("AverageDollarVolume expects exactly two inputs.");
        }

        torch::Tensor closes = inputs[0];
        torch::Tensor volumes = inputs[1];

        if (m_win == 1) {
            return closes * volumes;
        } else {
            if (closes.dim() == 3 && volumes.dim() == 3) {
                torch::Tensor dollar_volume = closes * volumes;
                return Spectre::nanmean(dollar_volume, 2, false);
            } else {
                throw std::runtime_error("Rolling AverageDollarVolume requires 3D input tensors");
            }
        }
    }
};

// Type aliases for convenience (similar to Python)
using MA = SimpleMovingAverage;
using SMA = SimpleMovingAverage;

} // namespace Spectre
